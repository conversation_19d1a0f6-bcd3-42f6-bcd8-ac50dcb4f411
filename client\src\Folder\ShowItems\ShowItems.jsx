import React from 'react';
import { Link } from 'react-router-dom';
import { ProjectAvatar } from 'shared/components';
import brand from '../../../dev/brand7.png';

const ShowItems = ({ items, folder }) => {
  if (items.length === 0) {
    return (
      <div className="ml-auto">
        <div className="flex flex-col justify-center items-center mb-20">
          <div className="text-8xl text-brand">
            <img
              src={brand}
              className="rounded-md object-cover w-40 h-auto sm:w-40 md:w-40 lg:w-40 xl:w-40 2xl:w-40 mt-7"
              alt="Logo"
            />
          </div>
          <div className="text-lg font-black mb-4 mt-4">This folder is empty.</div>
          <p className="pb-4">
            Add a notebook, import a document, or create a folder to fill it with life.
          </p>
          <button
            type="button"
            className="flex flex-row items-center justify-between w-28 text-white bg-blue-500 hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-full text-sm px-3 py-1 text-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="currentColor"
              className="mr-2"
            >
              <path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z" />
            </svg>

            <span className="mx-2">New</span>

            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24px"
              viewBox="0 -960 960 960"
              width="24px"
              fill="#D9D9D9"
            >
              <path d="M480-344 240-584l56-56 184 184 184-184 56 56-240 240Z" />
            </svg>
          </button>
        </div>
      </div>
    );
  }
  return (
    <div className="w-100">
      <div className="grid grid-cols-7 gap-4">
        {items &&
          items.map((item, index) => {
            return (
              <div key={`${index}${item.type}`} className="">
                {item.type === 'folder' && (
                  <Link key={`${index}${item.type}`} to={`/folder/${item.id}`}>
                    <img src={folder} width={100} height={200} alt="Logo" />
                    {item.name}
                  </Link>
                )}
                {item.type === 'project' && (
                  <Link key={`${index}${item.type}`} to={`/project/${item.id}/board`}>
                    <div className="flex flex-col justify-between mt-4">
                      <ProjectAvatar />
                      <span className="mt-3">{item.name}</span>
                    </div>
                  </Link>
                )}
              </div>
            );
          })}
      </div>
    </div>
  );
};

export default ShowItems;
