import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { fileURLToPath, URL } from 'node:url';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // allows us to do absolute imports from "src"
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      App: fileURLToPath(new URL('./src/App', import.meta.url)),
      Auth: fileURLToPath(new URL('./src/Auth', import.meta.url)),
      Folder: fileURLToPath(new URL('./src/Folder', import.meta.url)),
      Project: fileURLToPath(new URL('./src/Project', import.meta.url)),
      shared: fileURLToPath(new URL('./src/shared', import.meta.url)),
    },
  },
  server: {
    port: 3000,
    open: true,
    host: true,
  },
  build: {
    outDir: 'build',
    sourcemap: true,
  },
  css: {
    postcss: './postcss.config.js',
  },
});
