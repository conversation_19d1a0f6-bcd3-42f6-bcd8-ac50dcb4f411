import { useState, useRef, useEffect } from 'react';
import api from 'shared/utils/api';

export const useDeleteFolder = (fetchFolders, closeModal) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  const isMounted = useRef(true);

  useEffect(() => {
    // Mark as mounted when the component is mounted
    isMounted.current = true;

    // Cleanup function to mark as unmounted
    return () => {
      isMounted.current = false;
    };
  }, []);

  const deleteFolder = async (id) => {
    if (!id) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await api.delete(`/folder/${id}`);
      console.log('API response:', response.data);

      if (isMounted.current) {
        if (fetchFolders) fetchFolders();
        if (closeModal) closeModal();
      }
    } catch (err) {
      console.error('Error creating folder:', err);
      setError(err);
    } finally {
      if (isMounted.current) {
        setIsSubmitting(false);
      }
    }
  };

  return { deleteFolder, isSubmitting, error };
};
