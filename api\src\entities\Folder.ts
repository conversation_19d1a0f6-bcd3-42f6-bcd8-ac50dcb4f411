import {
  BaseEntity,
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  BeforeInsert,
  BeforeUpdate,
  OneToMany,
} from 'typeorm';
import is from 'utils/validation';
import Project from './Project';

@Entity()
class Folder extends BaseEntity {
  // Validations if needed (you can add custom validations for fields if required)
  static validations = {
    name: [is.required(), is.maxLength(200)],
    createdBy: [is.required()],
  };

  // The primary key (id) will be an auto-increment integer
  @PrimaryGeneratedColumn('increment') // Default SERIAL
  id: number;

  // Created by field with integer type for user ID
  @Column('integer')
  createdBy: number;

  // Timestamp for when the document is created
  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  // Timestamp for the last time the document was accessed
  @Column('timestamptz', { nullable: true })
  lastAccessed: Date;

  // Name of the document
  @Column('text')
  name: string;

  // Timestamp for the last time the document was updated
  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Materialized path storing full path as string (e.g., '/1/2/3')
  @Column('text')
  path: string;

  // Optional field for path with names (e.g., '/root/folder1/subfolder')
  @Column('text', { nullable: true })
  pathNames: string;

  // Parent folder ID for a document
  @Column('integer', { nullable: true })
  parent: number | null;

  @OneToMany(
    () => Project,
    project => project.folder,
  )
  projects: Project[];

  // Optional logic that can be added, for example, to strip tags from a document description (if applicable)
  @BeforeInsert()
  @BeforeUpdate()
  async setPathNames(): Promise<void> {
    if (this.path) {
      // Split the path into an array of folder IDs
      const pathIds = this.path.split('/').filter(id => id); // Removes empty elements

      // Look up folder names for each folder ID in the path
      const folderNames = await Promise.all(
        pathIds.map(async id => {
          const folder = await Folder.findOne({ where: { id: Number(id) } }); // Assuming Folder entity exists
          return folder ? folder.name : `Folder-${id}`; // If not found, fall back to ID as name
        }),
      );

      // Join the folder names to form the full path
      this.pathNames = `/${folderNames.join('/')}`;
    }
  }
}

export default Folder;
