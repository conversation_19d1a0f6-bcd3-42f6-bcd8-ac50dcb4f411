import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFolderIdColumn1736606678094 implements MigrationInterface {
    name = 'AddFolderIdColumn1736606678094'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "foldertId"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" ADD "foldertId" integer`);
    }

}
