import { Folder, Project } from 'entities';
import { Request, Response } from 'express';
import { catchErrors } from 'errors';
import { createEntity, updateEntity } from 'utils/typeorm';

// If we want any details of particular Folder, we can use it either for breadcrum or for other details like getProjects, notes, etc.. for that particular folder.
//  Remenber that we are using this api for fetching details only and need to maintain seperate api for details as such for the requirement.
export const getFolderDetails = catchErrors(async (req, res) => {
  const { id } = req.params;
  const folder = await Folder.findOne({
    where: { id: Number(id) },
    // relations: ['projects'],
  });

  res.respond({ folder });
});

export const getSubFolders = catchErrors(async (req, res) => {
  const { parentId } = req.params;

  // Check if the parent folder exists
  const parentFolder = await Folder.findOne({
    where: { id: Number(parentId) },
  });

  if (!parentFolder) {
    // Explicitly set the HTTP status to 404
    return res.respond({
      message: `Parent folder with ID ${parentId} does not exist`,
      status: 404,
    });
  }

  // Fetch subfolders
  const folders = await Folder.createQueryBuilder('folder')
    .select(['folder.id', 'folder.name']) // Select only the required fields
    .where('folder.parent = :parentId', { parentId }) // Filter by parentId
    .getMany();

  // Fetch projects
  const projects = await Project.createQueryBuilder('project')
    .select(['project.id', 'project.name']) // Select only the required fields
    .where('project.folderId = :parentId', { parentId }) // Filter by folderId
    .getMany();

  const combined = [
    ...folders.map(folder => ({ id: folder.id, name: folder.name, type: 'folder' })),
    ...projects.map(project => ({ id: project.id, name: project.name, type: 'project' })),
  ];

  const sortedCombined = combined.sort((a, b) => {
    const nameA = a.name.toLowerCase();
    const nameB = b.name.toLowerCase();

    if (nameA < nameB) return -1; // "a" comes before "b"
    if (nameA > nameB) return 1; // "b" comes before "a"
    return 0; // They are the same
  });

  res.respond({ data: sortedCombined, status: 200 });
});

export const getRootFolders = catchErrors(async (_: Request, res: Response) => {
  const folders = await Folder.createQueryBuilder('folder')
    .select(['folder.id', 'folder.name']) // Select only the required field
    .where('folder.parent IS NULL') // Filter for root folders
    .getMany();

  const projects = await Project.createQueryBuilder('project')
    .select(['project.id', 'project.name']) // Select only the required field
    .where('project.folderId IS NULL')
    .getMany();

  const combined = [
    ...folders.map(folder => ({ id: folder.id, name: folder.name, type: 'folder' })),
    ...projects.map(project => ({ id: project.id, name: project.name, type: 'project' })),
  ];

  const sortedCombined = combined.sort((a, b) => {
    // Convert names to lowercase to perform case-insensitive sorting
    const nameA = a.name.toLowerCase();
    const nameB = b.name.toLowerCase();

    if (nameA < nameB) return -1; // "a" comes before "b"
    if (nameA > nameB) return 1; // "b" comes before "a"
    return 0; // They are the same
  });

  res.respond({ data: sortedCombined, status: 200 });
});

export const createFolder = catchErrors(async (req, res) => {
  const { name, createdBy, parent } = req.body;

  // Validate request body
  if (!name || !createdBy) {
    return res.status(400).respond({ message: 'Name and createdBy are required' });
  }

  let parentFolder = null;

  // Validate parent folder existence
  if (parent) {
    parentFolder = await Folder.findOne({ where: { id: parent } });

    if (!parentFolder) {
      return res.status(404).respond({ message: `Parent folder with ID ${parent} does not exist` });
    }
  }

  // Construct path and pathNames
  const path = parentFolder ? `${parentFolder.path}/${parentFolder.id}` : '';
  const pathNames = parentFolder ? `${parentFolder.pathNames}/${parentFolder.name}` : '';

  // Create the new folder
  const folder = await createEntity(Folder, {
    name,
    createdBy,
    parent: parentFolder ? parentFolder.id : null,
    path,
    pathNames,
  });

  res.respond({ folder });
});

export const updateFolder = catchErrors(async (req, res) => {
  const { id } = req.params;
  const { name } = req.body;
  const folder = await Folder.findOneOrFail({ where: { id: Number(id) } });

  if (name) {
    folder.name = name;
  }

  await updateEntity(Folder, id, req.body);

  res.respond({ folder });
});

export const deleteFolder = catchErrors(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).respond({ message: 'Folder ID is required' });
  }

  // Fetch the folder to ensure it exists
  const folder = await Folder.findOne({ where: { id: Number(id) } });

  if (!folder) {
    return res.status(404).respond({ message: 'Folder not found' });
  }

  // Recursive function to delete child folders and their projects
  const deleteFolderRecursively = async (folderId: number): Promise<void> => {
    // Find child folders
    const childFolders = await Folder.find({ where: { parent: folderId } });

    // Recursively delete child folders
    for (const child of childFolders) {
      await deleteFolderRecursively(child.id);
    }

    // Delete associated projects
    const associatedProjects = await Project.find({ where: { folderId } });
    if (associatedProjects.length > 0) {
      await Project.remove(associatedProjects);
    }

    // Delete the folder
    const folderToDelete = await Folder.findOne({ where: { id: folderId } });
    if (folderToDelete) {
      await Folder.remove(folderToDelete);
    }
  };

  // Start recursive deletion from the target folder
  await deleteFolderRecursively(Number(id));

  res.respond({
    message: 'Folder and all associated child folders and projects deleted successfully',
  });
});
