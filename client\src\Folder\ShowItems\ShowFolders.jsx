import React from 'react';
import folderimg from '../../../dev/folder.png';
import Folder from './Folder';
import NoFolders from './NoFolders';
import SkeletonFolders from './SkeletonFolders';
import NotFound from './NotFound';

const ShowFolders = ({ folders, isLoading, error, fetchFolders, status }) => {
  if (status === 404) {
    return <NotFound />;
  }
  if (Array.isArray(folders) && folders?.length === 0) {
    return <NoFolders fetchFolders={fetchFolders} />;
  }
  if (isLoading) {
    return <SkeletonFolders />;
  }
  if (error) {
    return <SkeletonFolders />;
  }
  return (
    <div className="w-100">
      <div className="grid grid-cols-7 gap-4">
        {folders &&
          folders.map((item, index) => {
            return (
              <div key={`${index}${item.type}`} className="">
                <Folder
                  item={item}
                  index={index}
                  folderimg={folderimg}
                  fetchFolders={fetchFolders}
                />
              </div>
            );
          })}
      </div>
    </div>
  );
};

export default ShowFolders;
