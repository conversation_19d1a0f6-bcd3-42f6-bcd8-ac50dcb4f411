import React from 'react';
import { BrowserRouter, Routes as RouterRoutes, Route, Navigate } from 'react-router-dom';

import Project from 'Project';
import Authenticate from 'Auth/Authenticate';
import PageError from 'shared/components/PageError';
import Login from 'Auth/GoogleLogin';
import Folder from 'Folder';

// Function to check if the user is authenticated
const isAuthenticated = () => 
  // Check if the authToken exists in localStorage
   localStorage.getItem('authToken') !== null
;

// Protected Route component
function ProtectedRoute({ children }) {
  return isAuthenticated() ? children : <Navigate to="/login" replace />;
}

function Routes() {
  return <BrowserRouter>
    <RouterRoutes>
      <Route path="/login" element={<Login />} />

      {/* Protected home route */}
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <Folder />
          </ProtectedRoute>
        }
      />

      <Route
        path="/folder/:id"
        element={
          <ProtectedRoute>
            <Folder />
          </ProtectedRoute>
        }
      />

      <Route
        path="/settings"
        element={
          <ProtectedRoute>
            <Folder />
          </ProtectedRoute>
        }
      />

      <Route path="/authenticate" element={<Authenticate />} />

      {/* Protected project route */}
      <Route
        path="/project/:id/*"
        element={
          <ProtectedRoute>
            <Project />
          </ProtectedRoute>
        }
      />

      {/* PageError for unmatched routes */}
      <Route path="*" element={<PageError />} />
    </RouterRoutes>
  </BrowserRouter>
}

export default Routes;
