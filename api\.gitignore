# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# TypeScript build output
dist/
build/
lib/
*.tsbuildinfo

# TypeScript declaration files (if auto-generated)
*.d.ts

# API specific files
uploads/
temp/
logs/

# Database files
*.sqlite
*.sqlite3
*.db

# Certificate files
*.pem
*.key
*.crt
*.p12
*.pfx

# JWT secrets and keys
jwt-*.key
*.secret

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Jest coverage
coverage/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# ESLint cache
.eslintcache

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Backup files
*.backup
*.bak
*.tmp

# PM2 logs
pm2.log
ecosystem.config.js

# Docker
.dockerignore
docker-compose.override.yml
