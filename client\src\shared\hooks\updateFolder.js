import { useState, useRef, useEffect } from 'react';
import api from 'shared/utils/api';

export const useUpdateFolder = (fetchFolders, closeModal) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  const isMounted = useRef(true);

  useEffect(() => {
    // Mark as mounted when the component is mounted
    isMounted.current = true;

    // Cleanup function to mark as unmounted
    return () => {
      isMounted.current = false;
    };
  }, []);

  const updateFolder = async (folderName, id) => {
    if (!folderName || !id) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const data = {
        name: folderName,
      };
      const response = await api.put(`/folder/${id}`, data);
      console.log('API response:', response.data);

      if (isMounted.current) {
        if (fetchFolders) fetchFolders();
        if (closeModal) closeModal();
      }
    } catch (err) {
      console.error('Error creating folder:', err);
      setError(err);
    } finally {
      if (isMounted.current) {
        setIsSubmitting(false);
      }
    }
  };

  return { updateFolder, isSubmitting, error };
};
