import React from 'react';
import { NavLink, useParams } from 'react-router-dom';

import { Icon } from 'shared/components';

import brand from '../../../../dev/deskroombrand.png';
import {
  Sidebar,
  ProjectInfo,
  // ProjectTexts,
  // ProjectName,
  // ProjectCategory,
  Divider,
  LinkItem,
  LinkText,
  NotImplemented,
} from './Styles';

function ProjectSidebar() {
  const { id } = useParams();

  return (
    <Sidebar>
      <ProjectInfo>
        <img
          src={brand}
          // width={300}
          // height={200}
          className="rounded-md object-cover w-40 h-auto sm:w-40 md:w-40 lg:w-40 xl:w-40 2xl:w-40 mt-7"
          alt="Logo"
        />
        {/* <ProjectAvatar />
        <ProjectTexts>
          <ProjectName>Zingtill</ProjectName>
          <ProjectCategory>Deskroom</ProjectCategory>
        </ProjectTexts> */}
      </ProjectInfo>

      {renderLinkItem(id, 'Home', 'board', '/')}
      {renderLinkItem(id, 'Project settings', 'settings', `/project/${id}/settings`)}
      <Divider />
      {renderLinkItem(id, 'Releases', 'shipping')}
      {renderLinkItem(id, 'Issues and filters', 'issues')}
      {renderLinkItem(id, 'Pages', 'page')}
      {renderLinkItem(id, 'Reports', 'reports')}
      {renderLinkItem(id, 'Components', 'component')}
    </Sidebar>
  );
}

const renderLinkItem = (projectId, text, iconType, path) => {
  const isImplemented = !!path;
  const linkItemProps = isImplemented ? { as: NavLink, to: path } : { as: 'div' };

  return (
    <LinkItem {...linkItemProps}>
      <Icon type={iconType} />
      <LinkText>{text}</LinkText>
      {!isImplemented && <NotImplemented>Not implemented</NotImplemented>}
    </LinkItem>
  );
};

export default ProjectSidebar;
