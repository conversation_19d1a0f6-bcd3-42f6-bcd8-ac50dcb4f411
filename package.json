{"name": "jira_clone", "version": "1.0.0", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"pre-commit": "cd api && npm run pre-commit && cd ../client && npm run pre-commit", "install-dependencies": "npm install && cd api && npm install && cd ../client && npm install", "build": "cd api && npm run build && cd ../client && npm run build", "start:production": "cd api && npm run start:production && cd ../client && npm run start:production"}, "devDependencies": {"husky": "^4.0.0-beta.5"}, "husky": {"hooks": {"pre-commit": "npm run pre-commit"}}, "dependencies": {}}