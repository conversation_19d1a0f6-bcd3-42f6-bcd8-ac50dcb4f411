{"compilerOptions": {"target": "es6", "module": "commonjs", "lib": ["dom", "es6", "es2017", "es2019", "esnext.asynciterable"], "sourceMap": true, "outDir": "./build", "removeComments": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": false, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "src", "paths": {"*": ["./*"]}, "types": ["node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true}, "exclude": ["node_modules"], "include": ["./src/**/*.ts"]}