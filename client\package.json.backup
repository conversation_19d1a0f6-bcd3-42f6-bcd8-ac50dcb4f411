{"name": "jira_client", "version": "1.0.0", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"start": "webpack-dev-server", "start:production": "pm2 start --name 'jira_client' server.js", "test:jest": "jest", "test:cypress": "node_modules/.bin/cypress open", "build": "rimraf build && webpack --config webpack.config.js --progress", "pre-commit": "lint-staged"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.0", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-decorators": "^7.7.4", "@babel/plugin-proposal-export-namespace-from": "^7.7.4", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-dynamic-import": "^7.7.4", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.7.4", "@cypress/webpack-preprocessor": "^4.1.1", "autoprefixer": "^10.4.20", "babel-eslint": "^10.0.3", "babel-loader": "^8.0.6", "css-loader": "^3.3.2", "cypress": "^3.8.1", "eslint": "^6.1.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^6.7.0", "eslint-plugin-cypress": "^2.8.1", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.17.0", "eslint-plugin-react-hooks": "^1.7.0", "file-loader": "^5.0.2", "html-webpack-plugin": "^3.2.0", "jest": "^24.9.0", "lint-staged": "^9.5.0", "postcss": "^8.4.49", "postcss-loader": "^4.3.0", "prettier": "^1.19.1", "rimraf": "^6.0.1", "style-loader": "^1.0.1", "tailwindcss": "^3.4.17", "url-loader": "^3.0.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.9.0"}, "dependencies": {"@4tw/cypress-drag-drop": "^1.3.0", "@reduxjs/toolkit": "^2.5.0", "axios": "^0.19.0", "clsx": "^2.1.1", "color": "^3.1.2", "compression": "^1.7.4", "core-js": "^3.4.7", "express": "^4.17.1", "express-history-api-fallback": "^2.2.1", "formik": "^2.1.1", "history": "^4.10.1", "jwt-decode": "^2.2.0", "lodash": "^4.17.15", "moment": "^2.24.0", "prop-types": "^15.7.2", "query-string": "^6.9.0", "quill": "^1.3.7", "react": "^16.12.0", "react-beautiful-dnd": "^12.2.0", "react-content-loader": "^4.3.3", "react-dom": "^16.12.0", "react-redux": "^9.2.0", "react-router-dom": "^5.1.2", "react-textarea-autosize": "^7.1.2", "react-toastify": "^11.0.2", "react-transition-group": "^4.3.0", "redux": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^3.1.0", "regenerator-runtime": "^0.13.3", "styled-components": "^4.4.1", "sweet-pubsub": "^1.1.2"}, "lint-staged": {"*.{js,jsx}": ["eslint --fix", "prettier --write", "git add"]}}