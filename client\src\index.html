<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <title>Arcaley Workroom</title>
    <style>
      #splash {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      #splash-content {
        font-size: 24px;
        font-family: Arial, sans-serif;
        color: #333;
        opacity: 1;
        /* animation: scaleUp 1.5s ease-in-out; */
      }

      @keyframes scaleUp {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.2);
        }
        100% {
          transform: scale(1.5);
        }
      }
    </style>
  </head>
  <body>
    <div id="splash">
      <div id="splash-content">Workroom</div>
    </div>
    <div id="root">
      <!-- <div id="splash-screen" style="text-align:center; padding-top: 50vh;">
        <h1>Loading...</h1>
      </div> -->
    </div>
    <script>
      setTimeout(() => {
        const splash = document.getElementById('splash');
        if (splash) {
          splash.style.display = 'none';
        }
      }, 1000);
    </script>
  </body>
</html>
