import React, { useEffect, useRef, useState } from 'react';

const EditableField = ({ name, setName, onSubmit }) => {
  const [isEditing, setIsEditing] = useState(false);

    const inputRef = useRef(null);

    useEffect(() => {
      if (inputRef.current && isEditing) {
        inputRef.current.focus();
      }
    }, [isEditing]);

    const handleName = () => {
      setIsEditing(true);
    };
    const handleBlur = () => {
      setIsEditing(false);
      if (onSubmit) {
        onSubmit();
      }
    };

    return isEditing ? (
      <input
        type="text"
        value={name}
        ref={inputRef}
        onChange={e => setName(e.target.value)}
        onBlur={handleBlur}
        className="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 block w-2/3 ps-4 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
        placeholder="Untitled"
      />
    ) : (
      <button type="button" onClick={handleName}>
        {name}
      </button>
    );
}

export default EditableField