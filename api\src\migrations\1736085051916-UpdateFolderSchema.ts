import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateFolderSchema1736085051916 implements MigrationInterface {
    name = 'UpdateFolderSchema1736085051916'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" ADD "folderId" integer`);
        await queryRunner.query(`ALTER TABLE "project" ADD CONSTRAINT "FK_6c3ba5c71b9ae31fa278096c4ef" FOREIGN KEY ("folderId") REFERENCES "folder"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "project" DROP CONSTRAINT "FK_6c3ba5c71b9ae31fa278096c4ef"`);
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "folderId"`);
    }

}
