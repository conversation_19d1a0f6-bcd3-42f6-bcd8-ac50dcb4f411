import { MigrationInterface, QueryRunner } from "typeorm";

export class UserEntitySchema1738566734786 implements MigrationInterface {
    name = 'UserEntitySchema1738566734786'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_1a8725d89cd65783f6cc0046fe7"`);
        await queryRunner.query(`CREATE TABLE "user_entity" ("id" SERIAL NOT NULL, "entityId" integer NOT NULL, "entityType" character varying NOT NULL, "accessType" json NOT NULL, "role" character varying NOT NULL, "userId" integer, CONSTRAINT "PK_b54f8ea623b17094db7667d8206" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "projectId"`);
        await queryRunner.query(`ALTER TABLE "issue" ADD "createdBy" integer NOT NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE "project" ADD "createdBy" integer NOT NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE "user_entity" ADD CONSTRAINT "FK_fa501a26ccfaded22ce966337e9" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_entity" DROP CONSTRAINT "FK_fa501a26ccfaded22ce966337e9"`);
        await queryRunner.query(`ALTER TABLE "project" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "issue" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "projectId" integer`);
        await queryRunner.query(`DROP TABLE "user_entity"`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_1a8725d89cd65783f6cc0046fe7" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
