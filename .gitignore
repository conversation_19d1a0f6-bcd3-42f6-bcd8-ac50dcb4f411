# Dependencies
node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Production builds
build/
dist/
**/build/
**/dist/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# SvelteKit build / generate output
.svelte-kit

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Temporary folders
tmp/
temp/

# Backup files
*.backup
*.bak
*.tmp

# Database files
*.sqlite
*.sqlite3
*.db

# Certificate files
*.pem
*.key
*.crt

# Archive files
*.zip
*.tar.gz
*.rar

# Full-stack project specific
# Client build outputs
client/build/
client/dist/

# API build outputs
api/build/
api/dist/
api/lib/

# Database files
*.sqlite
*.sqlite3
*.db
database.db

# Uploads and user content
uploads/
public/uploads/
static/uploads/

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# Kubernetes
*.yaml.local
*.yml.local

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Google Cloud
.gcloud/

# Azure
.azure/

# Heroku
.heroku/

# Netlify
.netlify/

# Vercel
.vercel/

# PM2
ecosystem.config.js
pm2.log

# Monitoring and analytics
.sentry/
.datadog/

# Testing
test-results/
playwright-report/
test-results.xml

# Documentation builds
docs/build/
docs/dist/

# Storybook
storybook-static/

# Design files
*.sketch
*.fig
*.xd
*.psd

# Video and audio files (if not part of the project)
*.mp4
*.avi
*.mov
*.mp3
*.wav

# Large files that should use Git LFS
*.zip
*.tar.gz
*.rar
*.7z
