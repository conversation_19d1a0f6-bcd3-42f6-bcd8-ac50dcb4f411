import { BaseEntity, Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import is from 'utils/validation';
import { User } from '.';

@Entity()
class UserEntity extends BaseEntity {
  static validations = {
    role: [is.required(), is.maxLength(100)],
  };

  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(
    () => User,
    user => user.userEntities,
    { onDelete: 'CASCADE' },
  )
  user: User;

  @Column()
  entityId: number; // Foreign key to either Project, Note, Task, etc.

  @Column()
  entityType: 'folder' | 'room' | 'project' | 'note' | 'task' | 'doc' | 'board'; // Identifies the entity type

  @Column('json')
  accessType: string[];

  @Column('varchar')
  role: string;
}

export default UserEntity;
